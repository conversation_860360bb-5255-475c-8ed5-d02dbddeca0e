import { Card, CardContent } from "@core/components/ui/card";

const TeamTSkills = ({ technical_skills, role_ids, team_mappings }) => {
  // console.log("technical_skills");
  // console.log(technical_skills);

  // console.log("role_ids");
  // console.log(role_ids);

  // console.log("team_mappings");
  // console.log(team_mappings);

  /*

    - role ids for the selected team (extract and save in array)
    - query role mapping for the role ids (multi query)
    - get the technical skills for the role mappings


 ----- 



 */

  const skillsForTheRole = technical_skills
    .map((skill) => {
      const hasMatchingRole = skill.role_mapping.some((mapping) =>
        role_ids.filter((role) => role.id === mapping.role_id)
      );

      if (hasMatchingRole) {
        return {
          id: skill.id,
          name: skill.skill_name
        };
      }
    })
    .filter(Boolean);

  console.log("skillsForTheRole");
  console.log(skillsForTheRole);

  const return_skill_level = (lvl) => {
    console.log("return_skill_level");

    return skillsForTheRole.map((skill) => {
    return technical_skills.map((skill) => {
      return skill.role_mapping.map((mapping) => {
        if (mapping.role_id === lvl) {
          // console.log("skill_level", mapping.skill_level, mapping.role_id);

          return (
            <Card className="min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white">
              <CardContent className="text-xs text-center tracking-tight p-0">
                <div className="inline-block align-middle">
                  {/* {role.id == skill.id ? mapping.skill_level : "n/a"} */}
                  {/* role: {mapping.role_id} */}
                  {/* rid: {role.id} */}
                  sk: {skill.id}
                  {/* {mapping.skill_level} {"| "} {skill.id} */}
                  {/* need to put n/a if the skill is not mapped to the role */}
                </div>
              </CardContent>
            </Card>
          );
        }
      });
    });
  };

  const skills_header = (technical_skills, role_ids) => {
    return technical_skills.map((skill) => {
      const hasMatchingRole = skill.role_mapping.some((mapping) =>
        role_ids.filter((role) => role.id === mapping.role_id)
      );

      return hasMatchingRole ? (
        <Card
          key={skill.id}
          className="min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl"
        >
          <CardContent className="font-semibold text-xs text-center p-1">
            {/* {skill.skill_name} */}
            {skill.id}
          </CardContent>
        </Card>
      ) : null;
    });
  };

  const skill_rows = (technical_skills, role_ids) => {
    return role_ids.map((role, index) => {
      console.log("role");
      console.log(role);

      return (
        <>
          <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
            <CardContent className={"font-bold text-sm text-center"}>
              <div className="inline-block align-middle">{role.role_name}</div>
            </CardContent>
          </Card>

          {/* pass the role id to the return_skill_level function */}

          {/* <Card className="min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white">
            <CardContent className="text-xs text-center tracking-tight p-0">
              {role_ids.id}
              <div className="inline-block align-middle">{role.id}</div>
            </CardContent>
          </Card> */}

          {return_skill_level(role.id, role_ids)}
        </>
      );
    });

    // return technical_skills.map((skill) => {
    //   const hasMatchingRole = skill.role_mapping.some((mapping) =>
    //     role_ids.some((role) => role.id === mapping.role_id)
    //   );

    //   return hasMatchingRole ? (
    //     <>
    //       <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
    //         <CardContent className={"font-bold text-sm text-center"}>
    //           <div className="inline-block align-middle">
    //             {role_ids[0].role_name}
    //           </div>
    //         </CardContent>
    //       </Card>

    //       <Card className="min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white">
    //         <CardContent className="text-xs text-center tracking-tight p-0">
    //           Independent Practitioner
    //         </CardContent>
    //       </Card>
    //     </>
    //   ) : null;
  };

  return (
    <>
      <Card className="min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
        <CardContent className={"font-extrabold text-lg text-center"}>
          Skills
        </CardContent>
      </Card>
      {skills_header(technical_skills, role_ids)}
      {skill_rows(technical_skills, role_ids)}
      {/* {return_skill_level(4)} */}
      {/* {return_levels(technical_skills, role_ids)} */}

      {/* <Card className="min-w-8  bg-[#1f144a] text-primary-foreground">
        <CardContent className={"font-bold text-sm text-center"}>
          <div className="inline-block align-middle">SOC Manager</div>
        </CardContent>
      </Card>

      <Card className="min-w-8  bg-[#9FC33F] hover:bg-primary hover:text-white">
        <CardContent className="text-xs text-center tracking-tight p-0">
          Independent Practitioner
        </CardContent>
      </Card> */}
    </>
  );

  //   const getLevel = (lvl) => {
  //     switch (lvl) {
  //       case 1:
  //         return "Operational Contributor";
  //       case 2:
  //         return "Advanced Contributor";
  //       case 3:
  //         return "Team Leader";
  //       case 4:
  //         return "Leader of Leaders";
  //       case 5:
  //         return "Organisational Leader";
  //     }
  //   };

  //   return (
  //     <>
  //       {" "}
  //       <Card className="min-w-8 bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-extrabold text-lg text-center"}>
  //           Skills
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           BCDR
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Change Management
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8  bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Certification Management
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold  text-xs text-center p-1"}>
  //           Compliance & Regulatory Assurance
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold  text-xs text-center p-1"}>
  //           Data Analytics and Insights
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Development Lifecycle
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Incident Response Lifecycle
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Infrastructure and Cloud Computing
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold  text-xs text-center p-1"}>
  //           Procurement
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Risk Management
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Supplier Management
  //         </CardContent>
  //       </Card>
  //       <Card className="min-w-8   bg-[#009cbb] text-primary-foreground rounded-tl-xl rounded-tr-xl">
  //         <CardContent className={"font-semibold text-xs text-center p-1"}>
  //           Threat Intelligence
  //         </CardContent>
  //       </Card>
  //     </>
  //   );
  //     filteredBSkills &&
  //     filteredBSkills.map((role) => {
  //       if (role.role_name === "Team Owner") {
  //         return null;
  //       }

  //       return (
  //         <>
  //           <Card className="min-w-8  bg-[#1f144a] text-primary-foreground ">
  //             <CardContent className={"font-semibold text-sm text-center"}>
  //               <div className="inline-block align-middle">{role.role_name}</div>
  //             </CardContent>
  //           </Card>

  //           <Card className="min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white">
  //             <CardContent className="text-sm text-center tracking-tight] ">
  //               {getLevel(role.behavioural_skill_level)}
  //             </CardContent>
  //           </Card>
  //           <Card className="min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white">
  //             <CardContent className="text-sm text-center tracking-tight">
  //               {getLevel(role.behavioural_skill_level)}
  //             </CardContent>
  //           </Card>
  //           <Card className="min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white">
  //             <CardContent className="text-sm text-center tracking-tight">
  //               {getLevel(role.behavioural_skill_level)}
  //             </CardContent>
  //           </Card>
  //           <Card className="min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white">
  //             <CardContent className="text-sm text-center tracking-tight">
  //               {getLevel(role.behavioural_skill_level)}
  //             </CardContent>
  //           </Card>
  //           <Card className="min-w-8 bg-[#f05b8f] hover:bg-primary text-black hover:text-white">
  //             <CardContent className="text-sm text-center tracking-tight">
  //               {getLevel(role.behavioural_skill_level)}
  //             </CardContent>
  //           </Card>
  //         </>
  //       );
  //     })
  //   );
};

export default TeamTSkills;
